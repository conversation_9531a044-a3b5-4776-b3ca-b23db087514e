from mcp_agents.document_mcp_agent import DocumentMCPAgent
from mcp_agents.web_mcp_agent import WebMCPAgent
import os

def route_question(question: str, doc_agent, web_agent) -> str:
    # Simple routing logic based on keywords
    keywords_for_web = ["latest", "current", "news", "update"]
    if any(keyword in question.lower() for keyword in keywords_for_web):
        agent = web_agent
    else:
        agent = doc_agent

    response = agent.handle_request({"question": question})
    return response.get("answer", "Sorry, I couldn't get an answer.")

def main():
    # Set your API keys here or export them in your shell environment
    os.environ["OPENAI_API_KEY"] = "your-openai-api-key"
    os.environ["GOOGLE_API_KEY"] = "your-google-api-key"
    os.environ["GOOGLE_CSE_ID"] = "your-google-cse-id"

    # Initialize agents
    doc_agent = DocumentMCPAgent("data/your_file.pdf")
    web_agent = WebMCPAgent()

    print("Welcome to the MCP 2-Agent Smart Chatbot!")
    print("Type 'exit' or 'quit' to stop.\n")

    while True:
        user_input = input("You: ")
        if user_input.strip().lower() in ["exit", "quit"]:
            print("Goodbye!")
            break

        answer = route_question(user_input, doc_agent, web_agent)
        print(f"Bot: {answer}")

if __name__ == "__main__":
    main()
