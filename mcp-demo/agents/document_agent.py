from langchain.document_loaders import PyPDFLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.vectorstores import FAISS
from langchain.embeddings import OpenAIEmbeddings
from langchain.chains import RetrievalQA
from langchain.llms import OpenAI
import os

class DocumentAgent:
    def __init__(self, pdf_path):
        self.pdf_path = pdf_path
        self._load_documents()
        self._build_vector_store()
        self._build_qa_chain()

    def _load_documents(self):
        loader = PyPDFLoader(self.pdf_path)
        self.documents = loader.load()

    def _build_vector_store(self):
        text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)
        texts = text_splitter.split_documents(self.documents)
        embeddings = OpenAIEmbeddings()
        self.vectorstore = FAISS.from_documents(texts, embeddings)

    def _build_qa_chain(self):
        llm = OpenAI(temperature=0)
        retriever = self.vectorstore.as_retriever()
        self.qa_chain = RetrievalQA.from_chain_type(llm=llm, retriever=retriever)

    def answer_question(self, question: str) -> str:
        return self.qa_chain.run(question)
