from mcp.agent import Agent
from agents.document_agent import DocumentAgent

class DocumentMCPAgent(Agent):
    def __init__(self, pdf_path):
        super().__init__(name="document_agent")
        self.doc_agent = DocumentAgent(pdf_path)

    def handle_request(self, request: dict) -> dict:
        question = request.get("question", "")
        answer = self.doc_agent.answer_question(question)
        return {"answer": answer}
